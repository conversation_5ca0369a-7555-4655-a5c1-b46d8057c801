/* Movie Card Styles */

/* Main Card Container */
.movie-card {
    -fx-background-color: white;
    -fx-background-radius: 20;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 12, 0, 0, 2);
    -fx-cursor: hand;
    -fx-border-color: rgba(0,0,0,0.05);
    -fx-border-width: 1;
    -fx-border-radius: 20;
    -fx-padding: 0;
    -fx-spacing: 0;
    -fx-background-insets: 0;
}

.movie-card:hover {
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 24, 0, 0, 8);
    -fx-translate-y: -6;
    -fx-border-color: rgba(0,123,255,0.2);
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
}

/* Poster Container */
.poster-container {
    -fx-background-radius: 20 20 0 0;
    -fx-clip: true;
    -fx-min-height: 420;
    -fx-max-height: 420;
}

.movie-poster {
    -fx-background-radius: 20 20 0 0;
    -fx-clip: true;
    -fx-smooth: true;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 8, 0, 0, 2);
}

/* Poster Placeholder */
.poster-placeholder {
    -fx-background-color: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -fx-background-radius: 20 20 0 0;
    -fx-min-height: 420;
    -fx-max-height: 420;
}

.placeholder-icon {
    -fx-icon-color: rgba(255,255,255,0.8);
}

.placeholder-text {
    -fx-font-size: 14px;
    -fx-text-fill: rgba(255,255,255,0.9);
    -fx-font-weight: 600;
}

/* Rating Badge */
.rating-badge {
    -fx-background-color: linear-gradient(45deg, #ffc107 0%, #ff8f00 100%);
    -fx-background-radius: 20;
    -fx-padding: 8 12;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 6, 0, 0, 2);
    -fx-border-color: rgba(255,255,255,0.3);
    -fx-border-width: 1;
    -fx-border-radius: 20;
}

.rating-star {
    -fx-icon-color: white;
}

.rating-text {
    -fx-text-fill: white;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
}

/* Favorite Button */
.favorite-btn {
    -fx-background-color: rgba(255,255,255,0.95);
    -fx-background-radius: 25;
    -fx-border-color: rgba(220,53,69,0.3);
    -fx-border-width: 2;
    -fx-border-radius: 25;
    -fx-padding: 10;
    -fx-min-width: 44;
    -fx-min-height: 44;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 6, 0, 0, 2);
}

.favorite-btn:hover {
    -fx-background-color: white;
    -fx-border-color: #dc3545;
    -fx-scale-x: 1.1;
    -fx-scale-y: 1.1;
    -fx-effect: dropshadow(gaussian, rgba(220,53,69,0.3), 8, 0, 0, 3);
}

.favorite-btn .ikonli-font-icon {
    -fx-icon-color: #dc3545;
}

.favorite-btn.favorited {
    -fx-background-color: #dc3545;
    -fx-border-color: #dc3545;
}

.favorite-btn.favorited .ikonli-font-icon {
    -fx-icon-literal: "fas-heart";
    -fx-icon-color: white;
}



/* Movie Info Section */
.movie-info {
    -fx-background-color: white;
    -fx-background-radius: 0 0 20 20;
    -fx-padding: 20 20 24 20;
    -fx-border-color: rgba(0,0,0,0.05);
    -fx-border-width: 1 0 0 0;
}

.movie-title {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #212529;
    -fx-line-spacing: 3;
    -fx-wrap-text: true;
    -fx-text-alignment: left;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.movie-year {
    -fx-font-size: 14px;
    -fx-text-fill: #6c757d;
    -fx-font-weight: 500;
    -fx-padding: 8 0 0 0;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}





/* List View Variant */
.movie-card.list-view {
    -fx-pref-width: -1;
    -fx-max-width: -1;
    -fx-orientation: horizontal;
}

.movie-card.list-view .poster-container {
    -fx-min-width: 120;
    -fx-max-width: 120;
    -fx-min-height: 180;
    -fx-max-height: 180;
    -fx-background-radius: 8;
}

.movie-card.list-view .movie-poster {
    -fx-fit-width: 120;
    -fx-fit-height: 180;
    -fx-background-radius: 8;
}

.movie-card.list-view .movie-info {
    -fx-background-radius: 0 16 16 0;
    -fx-padding: 20;
}

/* Responsive adjustments */
@media (max-width: 600px) {
    .movie-card {
        -fx-pref-width: 240;
        -fx-max-width: 240;
    }
    
    .poster-container {
        -fx-min-height: 360;
        -fx-max-height: 360;
    }
    
    .movie-poster {
        -fx-fit-width: 240;
        -fx-fit-height: 360;
    }
}

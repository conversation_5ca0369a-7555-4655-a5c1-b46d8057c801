package oop2.project.controller;

import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.VBox;
import javafx.stage.Modality;
import javafx.stage.Stage;
import oop2.project.model.Movie;
import oop2.project.Main;
import org.tinylog.Logger;

import java.io.IOException;
import java.util.Optional;

/**
 * Controller for individual movie card components.
 * Handles the presentation logic for displaying movie information in card format.
 */
public class MovieCardController {

    @FXML
    private ImageView moviePosterImage;
    
    @FXML
    private VBox posterPlaceholder;
    
    @FXML
    private Label ratingLabel;
    
    @FXML
    private Label movieTitleLabel;
    
    @FXML
    private Label movieYearLabel;

    @FXML
    private Button favoriteButton;

    @FXML
    private VBox movieCard;

    private Movie movie;

    /**
     * Initializes the movie card with the provided movie data.
     * 
     * @param movie the movie to display
     */
    public void setMovie(Movie movie) {
        if (movie == null) {
            Logger.warn("Attempted to set null movie in MovieCardController");
            return;
        }
        
        this.movie = movie;
        updateMovieDisplay();
    }

    /**
     * Updates all UI elements with the current movie data.
     */
    private void updateMovieDisplay() {
        updatePoster();
        updateBasicInfo();
        setupClickHandler();
    }

    /**
     * Updates the movie poster image or shows placeholder.
     */
    private void updatePoster() {
        String posterPath = movie.getPosterPath();
        String posterUrl = null;

        if (posterPath != null && !posterPath.isEmpty()) {
            posterUrl = "https://image.tmdb.org/t/p/w500" + posterPath;
        }

        if (posterUrl != null && !posterUrl.isEmpty()) {
            try {
                Image posterImage = new Image(posterUrl, true); // Load in background
                moviePosterImage.setImage(posterImage);
                moviePosterImage.setVisible(true);
                posterPlaceholder.setVisible(false);
            } catch (Exception e) {
                Logger.debug("Failed to load poster image for movie: {}", movie.getTitle());
                showPosterPlaceholder();
            }
        } else {
            showPosterPlaceholder();
        }
    }

    /**
     * Shows the poster placeholder when image is not available.
     */
    private void showPosterPlaceholder() {
        moviePosterImage.setVisible(false);
        posterPlaceholder.setVisible(true);
    }

    /**
     * Updates basic movie information (title, year, rating, duration).
     */
    private void updateBasicInfo() {
        movieTitleLabel.setText(Optional.ofNullable(movie.getTitle()).orElse("Unknown Title"));

        Integer year = extractReleaseYear();
        movieYearLabel.setText(year != null ? year.toString() : "Unknown");

        ratingLabel.setText(String.format("%.1f", movie.getVoteAverage()));
    }

    /**
     * Extracts the release year from the release date string.
     *
     * @return the release year or null if not available
     */
    private Integer extractReleaseYear() {
        String releaseDate = movie.getReleaseDate();
        if (releaseDate == null || releaseDate.isEmpty()) {
            return null;
        }

        try {
            // Release date format is typically "YYYY-MM-DD"
            return Integer.parseInt(releaseDate.substring(0, 4));
        } catch (Exception e) {
            Logger.debug("Failed to parse release year from: {}", releaseDate);
            return null;
        }
    }

    /**
     * Sets up click handler for the movie card to open detail view.
     */
    private void setupClickHandler() {
        if (movieCard != null) {
            movieCard.setOnMouseClicked(this::onCardClicked);
        }
    }

    /**
     * Handles movie card click to open detail view.
     */
    private void onCardClicked(MouseEvent event) {
        if (movie != null) {
            openMovieDetailView();
        }
    }

    /**
     * Opens the movie detail view in a new window.
     */
    private void openMovieDetailView() {
        try {
            FXMLLoader loader = new FXMLLoader(Main.class.getResource("view/MovieDetailView.fxml"));
            Parent root = loader.load();

            // Get the controller and set the movie
            MovieDetailViewController controller = loader.getController();
            controller.setMovie(movie);

            // Create and configure the stage
            Stage stage = new Stage();
            stage.setTitle(movie.getTitle() + " - Movie Details");
            stage.initModality(Modality.APPLICATION_MODAL);
            stage.setScene(new Scene(root));
            stage.setResizable(true);
            stage.setMinWidth(800);
            stage.setMinHeight(600);

            // Show the window
            stage.show();

            Logger.info("Opened movie detail view for: {}", movie.getTitle());
        } catch (IOException e) {
            Logger.error(e, "Failed to open movie detail view for: {}", movie.getTitle());
        }
    }

    /**
     * Gets the current movie.
     * 
     * @return the current movie
     */
    public Movie getMovie() {
        return movie;
    }
}
